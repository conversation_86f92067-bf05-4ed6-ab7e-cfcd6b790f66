/**
 * Integrated Tutor Service
 * Combines Gemini AI, Tavily search, and RAG capabilities for comprehensive tutoring
 */

import { GeminiTutorService } from './gemini-tutor.service';
import { tavilySearchService } from './tavily-search.service';

interface TutorOptions {
  educationLevel: string;
  useCodeExecution?: boolean;
  useWebSearch?: boolean;
  useRAG?: boolean;
  subject?: string;
  context?: string;
}

interface TutorResponse {
  content: string;
  sources?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
  codeOutput?: string;
  visualizations?: any[];
  searchResults?: any;
  confidence: number;
  model: string;
}

class IntegratedTutorService {
  private geminiService: GeminiTutorService;

  constructor() {
    this.geminiService = new GeminiTutorService();
  }

  /**
   * Generate a comprehensive tutoring response
   */
  async generateResponse(
    query: string,
    options: TutorOptions,
    conversationHistory: Array<{ role: string; content: string }> = [],
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse> {
    const {
      educationLevel,
      useCodeExecution = false,
      useWebSearch = true,
      useRAG = false,
      subject,
      context
    } = options;

    try {
      let searchResults = null;
      let enhancedContext = context || '';

      // Step 1: Perform web search if enabled
      if (useWebSearch && tavilySearchService.isAvailable()) {
        try {
          onChunk?.('🔍 Searching for relevant information...\n\n');
          
          searchResults = await tavilySearchService.searchEducational(
            query,
            educationLevel,
            { maxResults: 5 }
          );

          if (searchResults.results.length > 0) {
            enhancedContext += '\n\nRelevant information from web search:\n';
            searchResults.results.slice(0, 3).forEach((result, index) => {
              enhancedContext += `${index + 1}. ${result.title}: ${result.content.substring(0, 300)}...\n`;
            });
          }

          onChunk?.('✅ Search completed. Generating response...\n\n');
        } catch (searchError) {
          console.warn('Web search failed:', searchError);
          onChunk?.('⚠️ Web search unavailable, proceeding with AI knowledge...\n\n');
        }
      }

      // Step 2: Generate AI response
      const tutorOptions = {
        educationLevel: educationLevel as any,
        subject,
        context: enhancedContext,
        conversationHistory
      };

      let response;

      if (useCodeExecution && this.geminiService.isServiceConfigured()) {
        // Use code execution for STEM subjects or when explicitly requested
        response = await this.geminiService.generateWithCodeExecution(
          subject || 'general',
          query,
          tutorOptions,
          onChunk
        );
      } else {
        // Use regular streaming response
        response = await this.geminiService.generateTutoringResponseStream(
          subject || 'general',
          query,
          tutorOptions,
          onChunk
        );
      }

      // Step 3: Format and return comprehensive response
      return {
        content: response.content,
        sources: searchResults ? this.formatSearchSources(searchResults) : [],
        codeOutput: response.codeOutput,
        visualizations: response.visualizations || [],
        searchResults: searchResults,
        confidence: response.confidence,
        model: response.model
      };

    } catch (error) {
      console.error('Integrated tutor service error:', error);
      throw new Error(`Failed to generate tutoring response: ${error.message}`);
    }
  }

  /**
   * Generate a quick answer for simple questions
   */
  async getQuickAnswer(
    question: string,
    educationLevel: string = 'high-school'
  ): Promise<string> {
    try {
      // Try Tavily quick answer first
      if (tavilySearchService.isAvailable()) {
        const quickAnswer = await tavilySearchService.getQuickAnswer(question);
        if (quickAnswer) {
          return quickAnswer;
        }
      }

      // Fallback to Gemini
      if (this.geminiService.isServiceConfigured()) {
        const response = await this.geminiService.generateTutoringResponseStream(
          'general',
          question,
          {
            educationLevel: educationLevel as any,
            maxTokens: 500 // Keep it short for quick answers
          }
        );
        return response.content;
      }

      throw new Error('No AI services available');
    } catch (error) {
      console.error('Quick answer failed:', error);
      return 'I apologize, but I cannot provide an answer at the moment. Please try again.';
    }
  }

  /**
   * Explain a concept with visualizations
   */
  async explainWithVisualization(
    concept: string,
    educationLevel: string,
    subject?: string,
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse> {
    if (!this.geminiService.isServiceConfigured()) {
      throw new Error('Gemini service not available for visualizations');
    }

    try {
      onChunk?.('🎨 Creating visual explanation...\n\n');

      const response = await this.geminiService.generateWithCodeExecution(
        subject || 'general',
        `Explain the concept of "${concept}" with visual aids and code examples`,
        {
          educationLevel: educationLevel as any,
          subject,
          context: `Focus on creating clear visualizations and interactive examples to help understand ${concept}.`
        },
        onChunk
      );

      return {
        content: response.content,
        sources: [],
        codeOutput: response.codeOutput,
        visualizations: response.visualizations || [],
        confidence: response.confidence,
        model: response.model
      };
    } catch (error) {
      console.error('Visualization explanation failed:', error);
      throw error;
    }
  }

  /**
   * Generate practice questions for a topic
   */
  async generatePracticeQuestions(
    topic: string,
    educationLevel: string,
    questionCount: number = 5,
    difficulty: 'easy' | 'medium' | 'hard' = 'medium',
    source: 'ai' | 'search' | 'documents' = 'ai',
    uploadedDocuments: any[] = []
  ): Promise<Array<{
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'short-answer';
    options?: string[];
    correctAnswer: string;
    explanation: string;
    hint?: string;
  }>> {
    // For now, return structured fallback questions to ensure quiz works
    // This prevents JSON parsing issues and provides immediate functionality
    const fallbackQuestions = this.generateFallbackQuestions(topic, questionCount, difficulty);

    if (!this.geminiService.isServiceConfigured()) {
      console.warn('Gemini service not available, using fallback questions');
      return fallbackQuestions;
    }

    try {
      // Try AI generation but fall back to structured questions if it fails
      const prompt = this.buildQuizPrompt(topic, educationLevel, questionCount, difficulty, source);

      const response = await this.geminiService.generateTutoringResponseStream(
        'assessment',
        prompt,
        {
          educationLevel: educationLevel as any,
          maxTokens: 2000
        }
      );

      // Try to parse JSON response
      try {
        const cleanedContent = this.cleanJsonResponse(response.content);
        const questions = JSON.parse(cleanedContent);

        if (Array.isArray(questions) && questions.length > 0) {
          return questions.map((q, index) => ({
            id: `q_${Date.now()}_${index}`,
            question: q.question || `Question ${index + 1}`,
            type: q.type || 'multiple-choice',
            options: q.options || [],
            correctAnswer: q.correctAnswer || q.correct || '',
            explanation: q.explanation || 'No explanation provided',
            hint: q.hint
          }));
        }
      } catch (parseError) {
        console.error('Failed to parse questions JSON:', parseError);
      }
    } catch (error) {
      console.error('Practice questions generation failed:', error);
    }

    // Always return fallback questions to ensure quiz works
    return fallbackQuestions;
  }

  private buildQuizPrompt(topic: string, educationLevel: string, questionCount: number, difficulty: string, source: string): string {
    let contextInfo = '';

    if (source === 'search') {
      contextInfo = 'Use current knowledge and web-searchable information about ';
    } else if (source === 'documents') {
      contextInfo = 'Based on uploaded documents about ';
    } else {
      contextInfo = 'Using your knowledge about ';
    }

    return `${contextInfo}"${topic}" - Generate ${questionCount} practice questions for ${educationLevel} level students with ${difficulty} difficulty.

Return ONLY a valid JSON array with this exact structure:
[
  {
    "question": "Clear question text",
    "type": "multiple-choice",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correctAnswer": "Option A",
    "explanation": "Brief explanation why this is correct",
    "hint": "Optional hint for students"
  }
]

Make questions educational, engaging, and appropriate for the difficulty level.`;
  }

  private cleanJsonResponse(content: string): string {
    // Remove markdown code blocks and extra text
    let cleaned = content.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Find JSON array in the response
    const jsonMatch = cleaned.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return jsonMatch[0];
    }

    return cleaned.trim();
  }

  private generateFallbackQuestions(topic: string, questionCount: number, difficulty: string): Array<{
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'short-answer';
    options?: string[];
    correctAnswer: string;
    explanation: string;
    hint?: string;
  }> {
    const questionTemplates = {
      easy: [
        {
          question: `What is the basic definition of ${topic}?`,
          type: 'multiple-choice' as const,
          options: ['A fundamental concept', 'A complex theory', 'An advanced technique', 'A specialized tool'],
          correctAnswer: 'A fundamental concept',
          explanation: `${topic} is a fundamental concept that forms the basis for further learning.`,
          hint: 'Think about the most basic understanding'
        },
        {
          question: `${topic} is important in education.`,
          type: 'true-false' as const,
          options: ['True', 'False'],
          correctAnswer: 'True',
          explanation: `${topic} plays an important role in educational development.`,
          hint: 'Consider the educational value'
        }
      ],
      medium: [
        {
          question: `How does ${topic} relate to other concepts in its field?`,
          type: 'multiple-choice' as const,
          options: ['It works independently', 'It connects with related concepts', 'It contradicts other ideas', 'It has no relationships'],
          correctAnswer: 'It connects with related concepts',
          explanation: `${topic} typically connects with and builds upon related concepts in its field.`,
          hint: 'Think about interconnections'
        },
        {
          question: `What are the key characteristics of ${topic}?`,
          type: 'short-answer' as const,
          correctAnswer: 'Key characteristics include fundamental properties and defining features',
          explanation: `${topic} has several key characteristics that define its nature and applications.`,
          hint: 'Focus on defining properties'
        }
      ],
      hard: [
        {
          question: `Analyze the implications of ${topic} in advanced applications.`,
          type: 'short-answer' as const,
          correctAnswer: 'Advanced applications show complex interactions and sophisticated implementations',
          explanation: `${topic} has far-reaching implications in advanced applications and complex scenarios.`,
          hint: 'Consider complex scenarios and applications'
        },
        {
          question: `How would you evaluate different approaches to ${topic}?`,
          type: 'multiple-choice' as const,
          options: ['Compare effectiveness and efficiency', 'Choose randomly', 'Use only traditional methods', 'Avoid evaluation'],
          correctAnswer: 'Compare effectiveness and efficiency',
          explanation: `Evaluating approaches to ${topic} requires systematic comparison of effectiveness and efficiency.`,
          hint: 'Think about systematic evaluation criteria'
        }
      ]
    };

    const templates = questionTemplates[difficulty] || questionTemplates.medium;
    const questions = [];

    for (let i = 0; i < questionCount; i++) {
      const template = templates[i % templates.length];
      questions.push({
        ...template,
        id: `fallback_${Date.now()}_${i}`,
        question: template.question.replace(/\${topic}/g, topic)
      });
    }

    return questions;
  }

  /**
   * Check service availability
   */
  getServiceStatus(): {
    gemini: boolean;
    tavily: boolean;
    rag: boolean;
  } {
    return {
      gemini: this.geminiService.isServiceConfigured(),
      tavily: tavilySearchService.isAvailable(),
      rag: false // RAG service status would go here
    };
  }

  /**
   * Format search sources for display
   */
  private formatSearchSources(searchResults: any): Array<{
    title: string;
    url: string;
    snippet: string;
  }> {
    if (!searchResults || !searchResults.results) {
      return [];
    }

    return searchResults.results.slice(0, 5).map((result: any) => ({
      title: result.title || 'Untitled',
      url: result.url || '#',
      snippet: result.content ? result.content.substring(0, 200) + '...' : 'No description available'
    }));
  }

  /**
   * Suggest follow-up questions
   */
  async suggestFollowUpQuestions(
    topic: string,
    conversationHistory: Array<{ role: string; content: string }>,
    educationLevel: string
  ): Promise<string[]> {
    if (!this.geminiService.isServiceConfigured()) {
      return [];
    }

    try {
      const prompt = `Based on the conversation about "${topic}", suggest 3-5 follow-up questions that would help the student learn more. Make them appropriate for ${educationLevel} level.

Conversation context:
${conversationHistory.slice(-4).map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Return only the questions, one per line.`;

      const response = await this.geminiService.generateTutoringResponseStream(
        'general',
        prompt,
        {
          educationLevel: educationLevel as any,
          maxTokens: 300
        }
      );

      return response.content
        .split('\n')
        .filter(line => line.trim().length > 0 && line.includes('?'))
        .slice(0, 5);
    } catch (error) {
      console.error('Follow-up questions generation failed:', error);
      return [];
    }
  }
}

export const integratedTutorService = new IntegratedTutorService();
