/**
 * Interactive Games Component
 * Provides engaging educational games and interactive learning experiences
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Gamepad2, 
  Trophy, 
  Target, 
  Brain,
  BookOpen,
  Zap,
  Award,
  Medal,
  Crown,
  Flame,
  CheckCircle,
  Lock,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Shield,
  Heart,
  Play,
  Star,
  Timer,
  RotateCcw,
  Shuffle
} from "lucide-react";
import { ResearchDocument } from '../types';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';

interface InteractiveGamesProps {
  selectedDocument: ResearchDocument | null;
  uploadedDocuments: ResearchDocument[];
}

interface GameCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  category: 'Memory' | 'Logic' | 'Knowledge' | 'Speed' | 'Creative';
  requiresDocument: boolean;
  color: string;
}

const availableGames: GameCard[] = [
  {
    id: 'word-association',
    title: 'Word Association',
    description: 'Connect related concepts and terms from your learning materials',
    icon: Brain,
    difficulty: 'Easy',
    estimatedTime: '5-10 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-blue-500'
  },
  {
    id: 'concept-builder',
    title: 'Concept Builder',
    description: 'Build concept maps by connecting ideas and relationships',
    icon: Target,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Logic',
    requiresDocument: false,
    color: 'bg-green-500'
  },
  {
    id: 'quick-quiz',
    title: 'Lightning Quiz',
    description: 'Fast-paced questions to test your knowledge',
    icon: Zap,
    difficulty: 'Medium',
    estimatedTime: '3-5 min',
    category: 'Speed',
    requiresDocument: false,
    color: 'bg-yellow-500'
  },
  {
    id: 'memory-palace',
    title: 'Memory Palace',
    description: 'Create visual memory aids for complex information',
    icon: Crown,
    difficulty: 'Hard',
    estimatedTime: '15-20 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-purple-500'
  },
  {
    id: 'document-detective',
    title: 'Document Detective',
    description: 'Find key insights and connections in your uploaded documents',
    icon: BookOpen,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Knowledge',
    requiresDocument: true,
    color: 'bg-indigo-500'
  },
  {
    id: 'creative-synthesis',
    title: 'Creative Synthesis',
    description: 'Combine ideas from multiple sources to create new insights',
    icon: Lightbulb,
    difficulty: 'Hard',
    estimatedTime: '20-25 min',
    category: 'Creative',
    requiresDocument: true,
    color: 'bg-pink-500'
  }
];

export function InteractiveGames({ selectedDocument, uploadedDocuments }: InteractiveGamesProps) {
  const [activeGame, setActiveGame] = useState<string | null>(null);
  const [gameContent, setGameContent] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [gameProgress, setGameProgress] = useState(0);
  const [gameTopic, setGameTopic] = useState('');
  const [gameSource, setGameSource] = useState<'ai' | 'search' | 'documents'>('ai');
  const [gameStats, setGameStats] = useState({
    gamesPlayed: 12,
    totalScore: 2450,
    averageScore: 85,
    streak: 5,
    achievements: 8
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Memory': return 'bg-blue-100 text-blue-800';
      case 'Logic': return 'bg-green-100 text-green-800';
      case 'Knowledge': return 'bg-purple-100 text-purple-800';
      case 'Speed': return 'bg-orange-100 text-orange-800';
      case 'Creative': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const generateGameContent = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return null;

    setIsGenerating(true);
    setGameProgress(0);

    try {
      let content = null;
      const progressSteps = [20, 40, 60, 80, 100];

      for (let i = 0; i < progressSteps.length; i++) {
        setGameProgress(progressSteps[i]);
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      switch (gameId) {
        case 'word-association':
          content = await generateWordAssociationGame();
          break;
        case 'concept-builder':
          content = await generateConceptBuilderGame();
          break;
        case 'quick-quiz':
          content = await generateQuickQuizGame();
          break;
        case 'memory-palace':
          content = await generateMemoryPalaceGame();
          break;
        case 'document-detective':
          content = await generateDocumentDetectiveGame();
          break;
        case 'creative-synthesis':
          content = await generateCreativeSynthesisGame();
          break;
        default:
          content = { type: 'simple', message: 'Game content not implemented yet.' };
      }

      setGameContent(content);
      return content;
    } catch (error) {
      console.error('Game generation failed:', error);
      toast.error('Failed to generate game content');
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePlayGame = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return;

    if (game.requiresDocument && uploadedDocuments.length === 0) {
      toast.error('This game requires uploaded documents. Please upload a document first.');
      return;
    }

    setActiveGame(gameId);
    toast.success(`Starting ${game.title}...`);

    // Generate actual game content
    const content = await generateGameContent(gameId);
    if (!content) {
      setActiveGame(null);
      return;
    }
  };

  const handleGameComplete = () => {
    setActiveGame(null);
    setGameContent(null);
    toast.success('Game completed! +150 XP earned');
    setGameStats(prev => ({
      ...prev,
      gamesPlayed: prev.gamesPlayed + 1,
      totalScore: prev.totalScore + 150,
      streak: prev.streak + 1
    }));
  };

  // Game generation functions
  const generateWordAssociationGame = async () => {
    const topic = gameTopic || 'General Knowledge';

    // Generate topic-specific word pairs
    const topicPairs = generateTopicWordPairs(topic);

    return {
      type: 'word-association',
      title: `Word Association: ${topic}`,
      instructions: `Match these ${topic.toLowerCase()} terms with their related concepts!`,
      topic: topic,
      source: gameSource,
      pairs: topicPairs
    };
  };

  const generateTopicWordPairs = (topic: string) => {
    const topicMappings: Record<string, Array<{word1: string, word2: string, explanation: string}>> = {
      'Biology': [
        { word1: 'Photosynthesis', word2: 'Chlorophyll', explanation: 'Chlorophyll is the green pigment essential for photosynthesis in plants' },
        { word1: 'DNA', word2: 'Genetics', explanation: 'DNA carries genetic information in all living organisms' },
        { word1: 'Ecosystem', word2: 'Biodiversity', explanation: 'Biodiversity refers to the variety of life in an ecosystem' },
        { word1: 'Mitochondria', word2: 'Energy', explanation: 'Mitochondria are the powerhouses that produce energy in cells' },
        { word1: 'Evolution', word2: 'Natural Selection', explanation: 'Natural selection is the mechanism driving evolution' }
      ],
      'Physics': [
        { word1: 'Gravity', word2: 'Newton', explanation: 'Isaac Newton formulated the law of universal gravitation' },
        { word1: 'Energy', word2: 'Conservation', explanation: 'Energy cannot be created or destroyed, only transformed' },
        { word1: 'Wave', word2: 'Frequency', explanation: 'Frequency determines the properties of waves' },
        { word1: 'Atom', word2: 'Nucleus', explanation: 'The nucleus is the dense center of an atom' },
        { word1: 'Force', word2: 'Acceleration', explanation: 'Force causes acceleration according to Newton\'s second law' }
      ],
      'History': [
        { word1: 'Democracy', word2: 'Voting', explanation: 'Voting is a fundamental component of democratic systems' },
        { word1: 'Renaissance', word2: 'Art', explanation: 'The Renaissance was a period of great artistic achievement' },
        { word1: 'Revolution', word2: 'Change', explanation: 'Revolutions bring about significant social and political change' },
        { word1: 'Empire', word2: 'Territory', explanation: 'Empires control vast territories and populations' },
        { word1: 'Constitution', word2: 'Rights', explanation: 'Constitutions establish fundamental rights and laws' }
      ],
      'Mathematics': [
        { word1: 'Algebra', word2: 'Variables', explanation: 'Variables are symbols used to represent unknown values in algebra' },
        { word1: 'Geometry', word2: 'Shapes', explanation: 'Geometry studies the properties and relationships of shapes' },
        { word1: 'Calculus', word2: 'Derivatives', explanation: 'Derivatives measure rates of change in calculus' },
        { word1: 'Statistics', word2: 'Data', explanation: 'Statistics analyzes and interprets data patterns' },
        { word1: 'Probability', word2: 'Chance', explanation: 'Probability quantifies the likelihood of events' }
      ]
    };

    // Find matching topic or use general knowledge
    const matchingKey = Object.keys(topicMappings).find(key =>
      topic.toLowerCase().includes(key.toLowerCase()) || key.toLowerCase().includes(topic.toLowerCase())
    );

    if (matchingKey) {
      return topicMappings[matchingKey];
    }

    // Generate generic pairs for any topic
    return [
      { word1: topic, word2: 'Study', explanation: `${topic} requires dedicated study and practice` },
      { word1: 'Learning', word2: 'Understanding', explanation: 'Learning leads to deeper understanding' },
      { word1: 'Practice', word2: 'Improvement', explanation: 'Regular practice leads to improvement' },
      { word1: 'Knowledge', word2: 'Application', explanation: 'Knowledge becomes valuable through application' },
      { word1: 'Research', word2: 'Discovery', explanation: 'Research leads to new discoveries and insights' }
    ];
  };

  const generateConceptBuilderGame = async () => {
    const conceptMaps = [
      {
        centralConcept: 'Scientific Method',
        concepts: ['Hypothesis', 'Experiment', 'Observation', 'Analysis', 'Conclusion', 'Theory', 'Variables', 'Data']
      },
      {
        centralConcept: 'Ecosystem',
        concepts: ['Producers', 'Consumers', 'Decomposers', 'Food Chain', 'Habitat', 'Biodiversity', 'Climate', 'Adaptation']
      },
      {
        centralConcept: 'Democracy',
        concepts: ['Voting', 'Constitution', 'Rights', 'Government', 'Citizens', 'Elections', 'Laws', 'Freedom']
      }
    ];

    const randomMap = conceptMaps[Math.floor(Math.random() * conceptMaps.length)];

    return {
      type: 'concept-builder',
      title: 'Concept Map Builder',
      instructions: 'Connect these concepts to build understanding!',
      centralConcept: randomMap.centralConcept,
      concepts: randomMap.concepts
    };
  };

  const generateQuickQuizGame = async () => {
    const topic = gameTopic || 'General Knowledge';

    try {
      // Try to generate topic-specific questions using the integrated service
      if (gameTopic && integratedTutorService) {
        const questions = await integratedTutorService.generatePracticeQuestions(
          gameTopic,
          'intermediate',
          5,
          'medium',
          gameSource,
          uploadedDocuments
        );

        if (questions && questions.length > 0) {
          return {
            type: 'quick-quiz',
            title: `Lightning Quiz: ${topic}`,
            instructions: `Answer these ${topic.toLowerCase()} questions as quickly as possible!`,
            topic: topic,
            source: gameSource,
            questions: questions.map(q => ({
              question: q.question,
              options: q.options || [],
              correct: q.correctAnswer,
              explanation: q.explanation
            })),
            timeLimit: 30 // seconds per question
          };
        }
      }
    } catch (error) {
      console.error('Failed to generate topic-specific quiz:', error);
    }

    // Fallback to general questions
    const quizQuestions = [
      { question: 'What is the capital of France?', options: ['London', 'Berlin', 'Paris', 'Madrid'], correct: 'Paris', explanation: 'Paris has been the capital of France since 987 AD.' },
      { question: 'What is 2 + 2?', options: ['3', '4', '5', '6'], correct: '4', explanation: 'Basic addition: 2 + 2 = 4' },
      { question: 'Which planet is closest to the Sun?', options: ['Venus', 'Mercury', 'Earth', 'Mars'], correct: 'Mercury', explanation: 'Mercury is the innermost planet in our solar system.' },
      { question: 'What is the largest ocean on Earth?', options: ['Atlantic', 'Indian', 'Arctic', 'Pacific'], correct: 'Pacific', explanation: 'The Pacific Ocean covers about 46% of the water surface.' },
      { question: 'Who wrote "Romeo and Juliet"?', options: ['Charles Dickens', 'William Shakespeare', 'Jane Austen', 'Mark Twain'], correct: 'William Shakespeare', explanation: 'Shakespeare wrote this famous tragedy around 1595.' }
    ];

    // Randomly select 3-5 questions
    const selectedQuestions = quizQuestions.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 3) + 3);

    return {
      type: 'quick-quiz',
      title: topic ? `Lightning Quiz: ${topic}` : 'Lightning Quiz',
      instructions: 'Answer as quickly as possible!',
      topic: topic,
      source: gameSource,
      questions: selectedQuestions,
      timeLimit: 30 // seconds per question
    };
  };

  const generateMemoryPalaceGame = async () => {
    const memoryTopics = [
      {
        topic: 'Solar System Planets',
        items: ['Mercury', 'Venus', 'Earth', 'Mars', 'Jupiter', 'Saturn', 'Uranus', 'Neptune'],
        mnemonic: 'My Very Educated Mother Just Served Us Nachos'
      },
      {
        topic: 'Historical Periods',
        items: ['Ancient', 'Classical', 'Medieval', 'Renaissance', 'Industrial', 'Modern'],
        mnemonic: 'All Cats Make Really Interesting Meows'
      }
    ];

    const randomTopic = memoryTopics[Math.floor(Math.random() * memoryTopics.length)];

    return {
      type: 'memory-palace',
      title: 'Memory Palace Builder',
      instructions: 'Create visual memory aids for complex information!',
      topic: randomTopic.topic,
      items: randomTopic.items,
      mnemonic: randomTopic.mnemonic,
      content: `Learn to remember ${randomTopic.topic} using the memory palace technique!`
    };
  };

  const generateDocumentDetectiveGame = async () => {
    if (uploadedDocuments.length === 0) {
      return {
        type: 'document-detective',
        title: 'Document Detective',
        instructions: 'Upload documents to play this game!',
        content: 'No documents available for analysis. Upload some documents first to play this detective game!'
      };
    }

    return {
      type: 'document-detective',
      title: 'Document Detective',
      instructions: 'Find key insights in your documents!',
      documents: uploadedDocuments.slice(0, 3),
      challenges: [
        'Find the main thesis statement',
        'Identify key supporting evidence',
        'Locate methodology details',
        'Discover the conclusion',
        'Find any limitations mentioned'
      ]
    };
  };

  const generateCreativeSynthesisGame = async () => {
    const creativeChallenges = [
      {
        theme: 'Innovation',
        prompt: 'Combine concepts from technology and nature to create a new invention',
        examples: ['Biomimicry in robotics', 'Solar panel efficiency from leaf structures']
      },
      {
        theme: 'Problem Solving',
        prompt: 'Use principles from different fields to solve environmental challenges',
        examples: ['Economics + Ecology', 'Psychology + Urban Planning']
      }
    ];

    const randomChallenge = creativeChallenges[Math.floor(Math.random() * creativeChallenges.length)];

    return {
      type: 'creative-synthesis',
      title: 'Creative Synthesis Challenge',
      instructions: 'Combine ideas to create new insights!',
      theme: randomChallenge.theme,
      prompt: randomChallenge.prompt,
      examples: randomChallenge.examples,
      content: `Theme: ${randomChallenge.theme}\n\nChallenge: ${randomChallenge.prompt}`
    };
  };

  if (activeGame) {
    const game = availableGames.find(g => g.id === activeGame);

    if (isGenerating) {
      return (
        <div className="h-full flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
                <h3 className="text-xl font-bold mb-2">Generating {game?.title}</h3>
                <p className="text-gray-600">Creating your personalized game...</p>
              </div>
              <div className="space-y-4">
                <Progress value={gameProgress} className="w-full" />
                <p className="text-sm text-gray-500">{gameProgress}% complete</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setActiveGame(null);
                    setIsGenerating(false);
                  }}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (gameContent) {
      return (
        <div className="h-full p-6 overflow-y-auto">
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {game && <game.icon className="w-8 h-8 text-blue-500" />}
                  <div>
                    <CardTitle className="text-2xl">{gameContent.title || game?.title}</CardTitle>
                    <p className="text-gray-600">{gameContent.instructions}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setActiveGame(null)}
                >
                  Exit Game
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Game Content Display */}
                {gameContent.type === 'word-association' && gameContent.pairs && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {gameContent.pairs.map((pair: any, index: number) => (
                      <Card key={index} className="p-4 hover:shadow-md transition-shadow cursor-pointer hover:bg-blue-50">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-blue-600">{pair.word1}</span>
                          <span className="text-gray-400 text-xl">↔</span>
                          <span className="font-medium text-green-600">{pair.word2}</span>
                        </div>
                        <p className="text-sm text-gray-600">{pair.explanation}</p>
                      </Card>
                    ))}
                  </div>
                )}

                {gameContent.type === 'concept-builder' && gameContent.concepts && (
                  <div className="text-center">
                    <div className="mb-6">
                      <div className="inline-block p-4 bg-blue-100 rounded-lg shadow-md">
                        <h3 className="text-lg font-bold text-blue-800">{gameContent.centralConcept}</h3>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {gameContent.concepts.map((concept: string, index: number) => (
                        <div key={index} className="p-3 bg-gray-100 rounded-lg hover:bg-blue-100 cursor-pointer transition-colors shadow-sm">
                          <span className="text-sm font-medium">{concept}</span>
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 mt-4">Click on concepts to explore connections!</p>
                  </div>
                )}

                {gameContent.type === 'quick-quiz' && gameContent.questions && (
                  <div className="space-y-4">
                    {gameContent.questions.map((q: any, index: number) => (
                      <Card key={index} className="p-4">
                        <h4 className="font-medium mb-3">Question {index + 1}: {q.question}</h4>
                        {q.options && (
                          <div className="grid grid-cols-1 gap-2">
                            {q.options.map((option: string, optIndex: number) => (
                              <Button
                                key={optIndex}
                                variant="outline"
                                className="justify-start hover:bg-blue-50"
                                onClick={() => {
                                  const isCorrect = option === q.correct;
                                  toast.success(isCorrect ? `Correct! ${q.explanation || ''}` : `Incorrect. The correct answer is: ${q.correct}. ${q.explanation || ''}`);
                                }}
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                )}

                {gameContent.type === 'memory-palace' && gameContent.items && (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-xl font-bold mb-2">Topic: {gameContent.topic}</h3>
                      <p className="text-gray-600 mb-4">Memorize these items using the memory palace technique</p>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {gameContent.items.map((item: string, index: number) => (
                        <div key={index} className="p-3 bg-purple-100 rounded-lg text-center">
                          <span className="font-medium">{index + 1}. {item}</span>
                        </div>
                      ))}
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Memory Aid:</h4>
                      <p className="text-sm text-gray-700">{gameContent.mnemonic}</p>
                    </div>
                  </div>
                )}

                {gameContent.type === 'document-detective' && gameContent.challenges && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {gameContent.challenges.map((challenge: string, index: number) => (
                        <Card key={index} className="p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <span className="font-medium">{challenge}</span>
                          </div>
                        </Card>
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 text-center">Complete these challenges by analyzing your uploaded documents!</p>
                  </div>
                )}

                {gameContent.type === 'creative-synthesis' && gameContent.theme && (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-xl font-bold mb-2">Theme: {gameContent.theme}</h3>
                      <p className="text-gray-600 mb-4">{gameContent.prompt}</p>
                    </div>
                    {gameContent.examples && (
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Example Combinations:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {gameContent.examples.map((example: string, index: number) => (
                            <li key={index} className="text-sm text-gray-700">{example}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Think creatively and come up with your own unique combinations!</p>
                    </div>
                  </div>
                )}

                {/* Default content display for unstructured content */}
                {gameContent.content && !gameContent.pairs && !gameContent.concepts && !gameContent.questions && !gameContent.items && !gameContent.challenges && !gameContent.theme && (
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg">{gameContent.content}</div>
                  </div>
                )}

                <div className="flex justify-center pt-6">
                  <Button
                    onClick={handleGameComplete}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-2"
                  >
                    <Trophy className="w-4 h-4 mr-2" />
                    Complete Game
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <div className="h-full flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
              <h3 className="text-xl font-bold mb-2">{game?.title}</h3>
              <p className="text-gray-600">Loading game content...</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setActiveGame(null)}
              className="w-full"
            >
              Exit Game
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Interactive Learning Games</h1>
        <p className="text-gray-600">Engage with educational content through fun, interactive experiences</p>
      </div>

      {/* Topic and Source Selection */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Game Topic (Optional)
            </label>
            <input
              type="text"
              value={gameTopic}
              onChange={(e) => setGameTopic(e.target.value)}
              placeholder="e.g., Biology, Physics, History, Mathematics..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">Leave empty for general knowledge games</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Knowledge Source
            </label>
            <select
              value={gameSource}
              onChange={(e) => setGameSource(e.target.value as 'ai' | 'search' | 'documents')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ai">AI Knowledge</option>
              <option value="search">Web Search</option>
              <option value="documents" disabled={uploadedDocuments.length === 0}>
                Uploaded Documents {uploadedDocuments.length === 0 ? '(None available)' : `(${uploadedDocuments.length})`}
              </option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {gameSource === 'documents' && uploadedDocuments.length === 0
                ? 'Upload documents to use this source'
                : 'Games will be generated based on this source'}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.gamesPlayed}</p>
            <p className="text-sm text-gray-600">Games Played</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Star className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.totalScore}</p>
            <p className="text-sm text-gray-600">Total Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.averageScore}%</p>
            <p className="text-sm text-gray-600">Avg Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Flame className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.streak}</p>
            <p className="text-sm text-gray-600">Day Streak</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.achievements}</p>
            <p className="text-sm text-gray-600">Achievements</p>
          </CardContent>
        </Card>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableGames.map((game) => (
          <Card key={game.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-lg ${game.color}`}>
                  <game.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex space-x-2">
                  <Badge className={getDifficultyColor(game.difficulty)}>
                    {game.difficulty}
                  </Badge>
                  <Badge className={getCategoryColor(game.category)}>
                    {game.category}
                  </Badge>
                </div>
              </div>
              <CardTitle className="text-lg">{game.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{game.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center">
                  <Timer className="w-4 h-4 mr-1" />
                  {game.estimatedTime}
                </div>
                {game.requiresDocument && (
                  <div className="flex items-center">
                    <BookOpen className="w-4 h-4 mr-1" />
                    Requires Document
                  </div>
                )}
              </div>
              <Button 
                onClick={() => handlePlayGame(game.id)}
                className="w-full"
                disabled={game.requiresDocument && uploadedDocuments.length === 0}
              >
                <Play className="w-4 h-4 mr-2" />
                Play Game
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
